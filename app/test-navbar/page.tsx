'use client';

import { useState } from 'react';
import { Smartphone, Monitor, Tablet, Check, X } from 'lucide-react';

export default function TestNavbar() {
  const [currentView, setCurrentView] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-purple-400 mb-8">
          导航栏移动端兼容性测试
        </h1>

        {/* 视图切换器 */}
        <div className="mb-8 p-4 bg-gray-900 rounded-lg">
          <h2 className="text-lg font-semibold mb-4 text-white">测试不同设备视图</h2>
          <div className="flex gap-4">
            <button
              onClick={() => setCurrentView('mobile')}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                currentView === 'mobile'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <Smartphone className="w-4 h-4" />
              移动端 (< 768px)
            </button>
            <button
              onClick={() => setCurrentView('tablet')}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                currentView === 'tablet'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <Tablet className="w-4 h-4" />
              平板端 (768px - 1024px)
            </button>
            <button
              onClick={() => setCurrentView('desktop')}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                currentView === 'desktop'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <Monitor className="w-4 h-4" />
              桌面端 (> 1024px)
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 功能检查列表 */}
          <div className="space-y-6">
            <section className="bg-gray-900 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 text-white flex items-center gap-2">
                <Monitor className="w-5 h-5" />
                桌面端功能
              </h2>
              <ul className="space-y-3 text-gray-300">
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  完整的导航菜单显示
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  Categories 下拉菜单
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  搜索框始终可见
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  悬停效果和过渡动画
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  响应式搜索框宽度
                </li>
              </ul>
            </section>

            <section className="bg-gray-900 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 text-white flex items-center gap-2">
                <Smartphone className="w-5 h-5" />
                移动端功能
              </h2>
              <ul className="space-y-3 text-gray-300">
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  汉堡菜单按钮（左侧）
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  搜索按钮（右侧）
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  点击搜索按钮展开搜索框
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  侧边栏菜单（CategorySheet）
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  响应式布局适配
                </li>
                <li className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-400" />
                  触摸友好的按钮尺寸
                </li>
              </ul>
            </section>
          </div>

          {/* 测试指南 */}
          <div className="space-y-6">
            <section className="bg-gray-900 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 text-white">测试指南</h2>
              <div className="text-gray-300 space-y-4">
                <div>
                  <p className="font-semibold text-purple-400 mb-2">桌面端测试：</p>
                  <ul className="list-disc list-inside ml-4 space-y-1 text-sm">
                    <li>在宽屏幕上查看完整导航栏</li>
                    <li>点击 "Categories" 查看下拉菜单</li>
                    <li>使用搜索框功能</li>
                    <li>测试悬停效果</li>
                  </ul>
                </div>

                <div>
                  <p className="font-semibold text-purple-400 mb-2">移动端测试：</p>
                  <ul className="list-disc list-inside ml-4 space-y-1 text-sm">
                    <li>缩小浏览器窗口或使用开发者工具切换到移动端视图</li>
                    <li>点击左侧汉堡菜单按钮打开侧边栏</li>
                    <li>点击右侧搜索按钮展开搜索框</li>
                    <li>测试侧边栏中的导航链接</li>
                    <li>验证触摸目标大小是否合适</li>
                  </ul>
                </div>
              </div>
            </section>

            <section className="bg-gray-900 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 text-white">响应式断点</h2>
              <div className="space-y-3 text-gray-300">
                <div className="flex justify-between items-center p-3 bg-gray-800 rounded">
                  <span>移动端</span>
                  <span className="text-purple-400">< 768px</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-800 rounded">
                  <span>平板端</span>
                  <span className="text-purple-400">768px - 1024px</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-800 rounded">
                  <span>桌面端</span>
                  <span className="text-purple-400">> 1024px</span>
                </div>
              </div>
            </section>
          </div>
        </div>


        {/* 技术实现详情 */}
        <div className="mt-8">
          <section className="bg-gray-900 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-white">技术实现详情</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-300">
              <div>
                <p className="font-semibold text-purple-400 mb-3">响应式设计：</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>使用 Tailwind CSS 的响应式类（md:hidden, md:block）</li>
                  <li>移动端优先的设计方法</li>
                  <li>灵活的布局系统</li>
                  <li>触摸友好的交互设计</li>
                  <li>可访问性支持（ARIA 标签）</li>
                </ul>
              </div>

              <div>
                <p className="font-semibold text-purple-400 mb-3">组件架构：</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Navbar 主导航组件</li>
                  <li>CategorySheet 移动端侧边栏</li>
                  <li>NavigationMenu 桌面端下拉菜单</li>
                  <li>状态管理用于搜索框切换</li>
                  <li>Radix UI 组件库集成</li>
                </ul>
              </div>
            </div>
          </section>
        </div>

        {/* 性能优化 */}
        <div className="mt-6">
          <section className="bg-gray-900 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-white">性能优化</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-gray-300">
              <div className="p-4 bg-gray-800 rounded-lg">
                <h3 className="font-semibold text-purple-400 mb-2">懒加载</h3>
                <p className="text-sm">组件按需加载，减少初始包大小</p>
              </div>
              <div className="p-4 bg-gray-800 rounded-lg">
                <h3 className="font-semibold text-purple-400 mb-2">CSS优化</h3>
                <p className="text-sm">使用 Tailwind CSS 的 JIT 模式，只生成使用的样式</p>
              </div>
              <div className="p-4 bg-gray-800 rounded-lg">
                <h3 className="font-semibold text-purple-400 mb-2">动画优化</h3>
                <p className="text-sm">使用 CSS transforms 和 GPU 加速</p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
