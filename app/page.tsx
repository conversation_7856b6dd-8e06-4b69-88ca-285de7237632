import { getGames } from '@/lib/data';
import { GameCard } from '@/components/GameCard';
import { Star } from 'lucide-react';
import Link from 'next/link';

export default async function Home() {
  const games = await getGames();

  const RelatedGame = [
    {
      name:'dizzy love',
      coverImageUrl: '/small.gif',
      url:'/game/dizzy-love',
    },
    {
      name:'love test html',
      coverImageUrl: '/small.gif',
      url:'/game/love-test-html',
    },
    {
      name:'froggy love',
      coverImageUrl: '/small.gif',
      url:'/game/froggy-love',
    },
    {
      name:'love tester html5',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester-html5',
    },
    {
      name:'real love tester',
      coverImageUrl: '/small.gif',
      url:'/game/real-love-tester',
    },
    {
      name:'love tester',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester',
    },
    {
      name:'sweet love tester',
      coverImageUrl: '/small.gif',
      url:'/game/sweet-love-tester',
    },
    {
      name:'love tester fun love calculator',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester-fun-love-calculator',
    },
    {
      name:'love tester deluxe',
      coverImageUrl: '/small.gif',
      url:'/game/love_tester_deluxe',
    },
    {
      name:'bratz love meter',
      coverImageUrl: '/small.gif',
      url:'/game/bratz-love-meter',
    },
  ];
  const popluarGame = [
    {
      name:'dizzy love',
      coverImageUrl: '/small.gif',
      url:'/game/dizzy-love',
    },
    {
      name:'love test html',
      coverImageUrl: '/small.gif',
      url:'/game/love-test-html',
    },
    {
      name:'froggy love',
      coverImageUrl: '/small.gif',
      url:'/game/froggy-love',
    },
    {
      name:'love tester html5',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester-html5',
    },
    {
      name:'real love tester',
      coverImageUrl: '/small.gif',
      url:'/game/real-love-tester',
    },
    {
      name:'love tester',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester',
    },
    {
      name:'sweet love tester',
      coverImageUrl: '/small.gif',
      url:'/game/sweet-love-tester',
    },
    {
      name:'love tester fun love calculator',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester-fun-love-calculator',
    },
    {
      name:'love tester deluxe',
      coverImageUrl: '/small.gif',
      url:'/game/love_tester_deluxe',
    },
    {
      name:'bratz love meter',
      coverImageUrl: '/small.gif',
      url:'/game/bratz-love-meter',
    },
  ]
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Hero Section */}
        <div className="mb-12">
          {/* Game Title and Rating */}
          <div className="mb-8">
            <div className="text-center mb-6">
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent mb-4">
                Pirate Love Tester
              </h1>
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                Discover your romantic compatibility with this fun and magical love testing adventure!
              </p>
            </div>

            <div className="flex items-center justify-center gap-6 mb-6">
              <div className="flex items-center gap-2 bg-yellow-500/20 px-4 py-2 rounded-full">
                <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                <span className="text-lg font-semibold text-yellow-400">9.4</span>
                <span className="text-sm text-gray-400">/ 10</span>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">1,234</div>
                <div className="text-sm text-gray-400">Players</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">Free</div>
                <div className="text-sm text-gray-400">To Play</div>
              </div>
            </div>
          </div>

          {/* Main Game Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Game Frame */}
            <div className="lg:col-span-3">
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl overflow-hidden shadow-2xl border border-gray-700">
                <div className="bg-gray-800 px-6 py-4 border-b border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      </div>
                      <span className="text-gray-300 text-sm font-medium">Pirate Love Tester</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-400">
                      <span>🔒 Secure</span>
                      <span>•</span>
                      <span>📱 Mobile Friendly</span>
                    </div>
                  </div>
                </div>

                {/* Desktop Game */}
                <div className="hidden md:block">
                  <iframe
                    src="https://y8.com/embed/pirate_love_tester/"
                    className="w-full h-[500px] lg:h-[600px] border-0"
                    style={{overflow: 'hidden'}}
                    title="Pirate Love Tester Game"
                  />
                </div>
                {/* Mobile Game */}
                <div className="md:hidden">
                  <iframe
                    src="https://y8.com/embed/pirate_love_tester/"
                    className="w-full h-[400px] border-0"
                    style={{overflow: 'hidden'}}
                    title="Pirate Love Tester Game"
                  />
                </div>
              </div>

              {/* Game Controls */}
              <div className="mt-6 flex flex-wrap gap-4 justify-center">
                <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  🎮 Play Now
                </button>
                <button className="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  📱 Fullscreen
                </button>
                <button className="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  ❤️ Favorite
                </button>
              </div>
            </div>

            {/* Sidebar - Quick Play */}
            <div className="lg:col-span-1">
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl p-6 border border-gray-700">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                  ⚡ Quick Play
                </h3>
                <div className="space-y-3">
                  {RelatedGame.slice(0, 6).map((game, index) => (
                    <Link
                      key={index}
                      href={game.url}
                      className="group flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg hover:bg-purple-600/20 transition-all duration-200 border border-transparent hover:border-purple-500/30"
                    >
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-bold text-sm">
                          {game.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-medium truncate group-hover:text-purple-300 transition-colors capitalize">
                          {game.name}
                        </p>
                        <p className="text-gray-400 text-xs">Love Game</p>
                      </div>
                    </Link>
                  ))}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-700">
                  <Link
                    href="/category/love-games"
                    className="block w-full text-center bg-purple-600/20 hover:bg-purple-600/30 text-purple-300 py-3 rounded-lg font-medium transition-colors border border-purple-500/30"
                  >
                    View All Love Games →
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Game Description */}
        <section className="mb-16">
          <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl p-8 border border-gray-700">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">About Pirate Love Tester</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto rounded-full"></div>
            </div>

            <div className="max-w-4xl mx-auto">
              <p className="text-gray-300 text-lg leading-relaxed mb-8 text-center">
                Set sail on a journey of love and adventure with Love Tester, a lighthearted game that reveals how well you and your crush match up!
                Enter basic details like names, ages, heights, and eye colors, then let the system analyze your compatibility using its magical algorithm.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-xl p-6 border border-purple-500/30">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-2xl">🚀</span>
                    </div>
                    <h3 className="text-purple-300 font-semibold text-xl">Simple Mode</h3>
                  </div>
                  <p className="text-gray-300">Get instant results with a quick match! Perfect for those who want immediate answers about their romantic compatibility.</p>
                </div>

                <div className="bg-gradient-to-br from-pink-900/30 to-pink-800/20 rounded-xl p-6 border border-pink-500/30">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                      <span className="text-2xl">⭐</span>
                    </div>
                    <h3 className="text-pink-300 font-semibold text-xl">Advanced Mode</h3>
                  </div>
                  <p className="text-gray-300">Dive deeper with more inputs and see if the stars align for your perfect match. More detailed analysis awaits!</p>
                </div>
              </div>

              <div className="text-center mb-8">
                <p className="text-gray-300 text-lg leading-relaxed">
                  Whether you're curious, crushing, or just having fun with friends, Love Tester is the perfect game to bring a spark of romance and a smile to your day.
                </p>
              </div>

              <div className="bg-gradient-to-r from-purple-900/40 via-pink-900/40 to-purple-900/40 rounded-xl p-6 border border-purple-500/30 text-center">
                <p className="text-purple-200 font-medium text-lg mb-4">
                  💕 Are you and your crush a perfect match? 💕
                </p>
                <p className="text-gray-300 mb-4">
                  There's only one way to find out — play now and discover your romantic destiny!
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <span className="bg-purple-600/30 text-purple-300 px-4 py-2 rounded-full text-sm">❤️ Fun & Free</span>
                  <span className="bg-pink-600/30 text-pink-300 px-4 py-2 rounded-full text-sm">🎮 Easy to Play</span>
                  <span className="bg-purple-600/30 text-purple-300 px-4 py-2 rounded-full text-sm">✨ Magical Results</span>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Related Games */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Related Love Games</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Discover more romantic adventures and compatibility tests that will spark your curiosity
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mt-4 rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {games.slice(0, 8).map((game) => (
              <div key={game.id} className="group">
                <GameCard game={game} />
              </div>
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/category/love-games"
              className="inline-flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              View All Love Games
              <span className="text-lg">→</span>
            </Link>
          </div>
        </section>

        {/* Popular Games */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Most Popular Games</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Join thousands of players enjoying these trending games right now
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-500 mx-auto mt-4 rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {games.slice(0, 8).map((game) => (
              <div key={`popular-${game.id}`} className="group">
                <GameCard game={game} />
              </div>
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/popular"
              className="inline-flex items-center gap-2 bg-pink-600 hover:bg-pink-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              View All Popular Games
              <span className="text-lg">→</span>
            </Link>
          </div>
        </section>

        {/* Game Collection */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Complete Love Game Collection</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Explore our full collection of romantic games, compatibility tests, and love adventures
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-purple-500 mx-auto mt-4 rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {RelatedGame.map((game, index) => (
              <Link
                key={index}
                href={game.url}
                className="group bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl p-6 hover:from-purple-900/20 hover:to-pink-900/20 transition-all duration-300 border border-gray-700 hover:border-purple-500/50"
              >
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-xl">
                      {game.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-semibold group-hover:text-purple-300 transition-colors capitalize text-lg">
                      {game.name}
                    </h3>
                    <p className="text-gray-400 text-sm mt-1">Love & Romance Game</p>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-yellow-400 text-sm">⭐ 4.8</span>
                      <span className="text-gray-500 text-xs">•</span>
                      <span className="text-gray-400 text-xs">Free to Play</span>
                    </div>
                  </div>
                  <div className="text-purple-400 group-hover:text-purple-300 transition-colors">
                    <span className="text-2xl">→</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          <div className="mt-12 text-center">
            <div className="bg-gradient-to-r from-purple-900/30 via-pink-900/30 to-purple-900/30 rounded-xl p-8 border border-purple-500/30">
              <h3 className="text-2xl font-bold text-white mb-4">Ready to Find Your Perfect Match?</h3>
              <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
                Join millions of players who have discovered their romantic compatibility through our fun and engaging love games!
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105">
                  🎮 Start Playing Now
                </button>
                <button className="bg-gray-800 hover:bg-gray-700 text-white px-8 py-4 rounded-lg font-semibold transition-colors border border-gray-600">
                  📱 Download App
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}

