import Link from "next/link";
export default function foo() {
    const hotGame = [
    {
      name:'dizzy love',
      coverImageUrl: '/small.gif',
      url:'/game/dizzy-love',
    },
    {
      name:'love test html',
      coverImageUrl: '/small.gif',
      url:'/game/love-test-html',
    },
    {
      name:'froggy love',
      coverImageUrl: '/small.gif',
      url:'/game/froggy-love',
    },
    {
      name:'love tester html5',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester-html5',
    },
    {
      name:'real love tester',
      coverImageUrl: '/small.gif',
      url:'/game/real-love-tester',
    },
    {
      name:'love tester',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester',
    },
    {
      name:'sweet love tester',
      coverImageUrl: '/small.gif',
      url:'/game/sweet-love-tester',
    },
    {
      name:'love tester fun love calculator',
      coverImageUrl: '/small.gif',
      url:'/game/love-tester-fun-love-calculator',
    },
    {
      name:'love tester deluxe',
      coverImageUrl: '/small.gif',
      url:'/game/love_tester_deluxe',
    },
    {
      name:'bratz love meter',
      coverImageUrl: '/small.gif',
      url:'/game/bratz-love-meter',
    },
  ]
  return <div>
    <div className='grid grid-cols-8 gap-3'>
        {hotGame.map((game,i ) => (
          <div className='w-[180px] h-[135px] rounded-lg overflow-hidden bg-red-300' key={i}>
            <Link href={game.url}>
             {game.name}
            </Link>
          </div>
        ))}
      </div>
  </div>;
}
