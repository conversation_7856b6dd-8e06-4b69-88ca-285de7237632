
'use client';

import Link from 'next/link';
import { Search, Heart, MenuIcon } from 'lucide-react';
import { CategorySheet } from './category-sheet';
import { getCategories } from '@/lib/data';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuList } from './ui/navigation-menu';
import { NavigationMenuTrigger } from '@radix-ui/react-navigation-menu';
import { NavigationMenuLink } from '@radix-ui/react-navigation-menu';

type Categories = Awaited<ReturnType<typeof getCategories>>;

export function Navbar({categories, onMenuClick }: {categories: Categories; onMenuClick: () => void }) {
  return (
    <header className="bg-black text-white shadow-md">
      <div className="mx-auto px-4 py-3 flex justify-between items-center">
        <div className="flex items-center gap-4">
          
        <Link href="/" className="flex items-center space-x-2">
          <Heart className="text-purple-500" />
          <span className="text-xl font-bold">Love Tester</span>
        </Link>
        </div>
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger className='cursor-pointer text-weight'>Categories</NavigationMenuTrigger>
              <NavigationMenuContent>

              </NavigationMenuContent>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink className='cursor-pointer'>Hot Game</NavigationMenuLink>
              <NavigationMenuContent>
                
              </NavigationMenuContent>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuTrigger className='cursor-pointer'>Purple Games</NavigationMenuTrigger>
              <NavigationMenuContent>
                
              </NavigationMenuContent>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>

        <div className="mx-4 hidden md:block">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <input type="search" placeholder="Search..." className="bg-gray-800 text-white p-2 pl-10 rounded-full w-[500px] focus:outline-none focus:ring-2 focus:ring-purple-500" />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          
        </div>
      </div>
    </header>
  );
}
