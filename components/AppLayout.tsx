
'use client';

import { useState } from 'react';
import { CategorySheet } from "@/components/category-sheet";
import { CategoryDesktop } from '@/components/category-desktop'
import { Navbar } from "@/components/Navbar";
import { getCategories } from '@/lib/data';

export function AppLayout({
  children,
  categories,
}: {
  children: React.ReactNode;
  categories: Awaited<ReturnType<typeof getCategories>>;
}) {
  const [isSheetOpen, setSheetOpen] = useState(true);

  const handleClose = () => {
    
    setSheetOpen(isSheetOpen ? false : true);
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <Navbar categories={categories} onMenuClick={handleClose} />
      <div className="flex">
        
        <div className="w-full md:w-[1600px] p-4">
          {children}
        </div>
        
      </div>

      
    </div>
  );
}
