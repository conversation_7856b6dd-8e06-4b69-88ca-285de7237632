'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Gamepad2, X, Home, MenuIcon } from 'lucide-react';
import { getCategories } from '@/lib/data';
import { Sheet, SheetContent, SheetTrigger, SheetClose } from './ui/sheet';

interface CategorySheetProps {
  categories: Awaited<ReturnType<typeof getCategories>>;
}

export function CategorySheet({categories}: CategorySheetProps) {
  const pathname = usePathname();

  return (
    <>
        <Sheet>
          <SheetTrigger><MenuIcon /></SheetTrigger>
          <SheetContent side="left" className='bg-block' overlayClassName='bg-black/90'>        
            <div className="overflow-y-auto h-full pt-20">
          <nav>
            <ul className="space-y-2">
              {/* Home link */}
              <li>
                <SheetClose asChild>
                <Link
                  href="/"
                  className={`flex items-center rounded-lg hover:bg-gray-700 transition-colors ${
                    pathname === '/' ? 'hover:bg-purple-700' : ''
                  }`}
                > 
                  <Home className="w-[24px] h-[24px] px-3" />
                  <span className={`font-medium ml-3}`}>All Games</span>
                </Link>
                </SheetClose>
              </li>

              {/* Category links */}
              {categories.map((category) => {
                const isActive = pathname === `/category/${category.id}`;
                return (
                  <li key={category.id}>
                    <SheetClose asChild>
                    <Link
                      href={`/category/${category.id}`}
                      className={`flex items-center p-3 group rounded-lg hover:bg-gray-700 transition-colors ${
                        isActive ? 'bg-purple-600 hover:bg-purple-700' : ''
                      }`}
                    >
                      <Gamepad2 className="w-[24px] h-[24px]" />
                      <span className={`font-medium group-hover:ml-3 transition-all duration-300 ml-3`}>{category.name}</span>
                    </Link>
                    </SheetClose>
                  </li>
                );
              })}
            </ul>
          </nav>
          
        </div>
        </SheetContent>

        </Sheet>
    </>
  );
}
